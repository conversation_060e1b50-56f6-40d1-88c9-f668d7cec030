{"app": {"name": "TekRem ERP", "tagline": "Technology Remedies Innovations"}, "navigation": {"dashboard": "Dashbodi", "profile": "I<PERSON>kalile", "settings": "<PERSON><PERSON><PERSON><PERSON>", "logout": "<PERSON><PERSON>", "login": "Ingila", "register": "Ilembya", "home": "<PERSON>", "about": "Palwa Ifwe", "services": "<PERSON><PERSON><PERSON><PERSON>", "portfolio": "<PERSON><PERSON>", "contact": "<PERSON><PERSON><PERSON><PERSON>", "notifications": "<PERSON><PERSON><PERSON><PERSON>"}, "auth": {"email": "Email", "password": "<PERSON><PERSON><PERSON>", "remember_me": "Njibukishe", "forgot_password": "<PERSON><PERSON><PERSON> amashiwi yandi a<PERSON>?", "login": "Ingila", "register": "Ilembya", "name": "<PERSON><PERSON><PERSON>", "confirm_password": "<PERSON><PERSON><PERSON><PERSON>", "already_registered": "Bushe naulembwa kale?", "reset_password": "<PERSON><PERSON><PERSON>", "verify_email": "<PERSON><PERSON><PERSON><PERSON>", "resend_verification_email": "<PERSON><PERSON><PERSON>", "verification_link_sent": "Ubutantiko bupya ubwakushininkisha nabututumwa ku email yobe."}, "dashboard": {"title": "Dashbodi", "welcome": "<PERSON><PERSON><PERSON><PERSON> ku dashbodi yenu", "stats": "<PERSON><PERSON><PERSON>", "recent_activity": "<PERSON><PERSON>", "quick_actions": "<PERSON><PERSON>"}, "admin": {"users": "Abomfwi", "roles": "<PERSON><PERSON><PERSON><PERSON>", "permissions": "Insambu", "settings": "<PERSON><PERSON><PERSON><PERSON>", "logs": "Ifyalembwa", "system": "<PERSON><PERSON><PERSON>"}, "crm": {"title": "CRM", "clients": "Abakasitoma", "leads": "Abalefwaya", "communications": "Ukwishibana", "opportunities": "<PERSON><PERSON><PERSON><PERSON>", "chats": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chat": "LiveChat", "conversation": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "analytics": "Ukusala", "dashboard": "Dashbodi"}, "finance": {"title": "<PERSON><PERSON>", "accounts": "<PERSON><PERSON><PERSON><PERSON>", "transactions": "Amatransaction", "invoices": "Amainvoice", "payments": "Ukulipila", "quotations": "Amaquotation", "expenses": "Inkongole", "budgets": "<PERSON><PERSON><PERSON><PERSON>", "categories": "Amacategory", "reports": "Am<PERSON><PERSON><PERSON>", "income_statement": "<PERSON><PERSON><PERSON>", "cash_flow": "Ukuya kwa Mali", "balance_sheet": "Balance Sheet", "expense_report": "Ilipoti lya Inkongole", "total_balance": "Balance Yonse", "monthly_income": "Income ya Mweshi", "monthly_expenses": "Inkongole sha <PERSON>", "net_income": "Net Income", "pending_invoices": "Amainvoice Ayalindila", "overdue_invoices": "Amainvoice Ayacila"}, "hr": {"title": "HR", "employees": "Ababomfi", "leave": "<PERSON><PERSON><PERSON>", "attendance": "Ukwisa", "performance": "Incito"}, "projects": {"title": "Imipango", "projects": "Imipango", "tasks": "Incito", "timelines": "<PERSON><PERSON><PERSON>", "resources": "Ifipe"}, "support": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tickets": "Amaticket", "knowledge_base": "Ukwishiba", "faq": "<PERSON><PERSON><PERSON><PERSON>"}, "analytics": {"title": "Ukusala", "reports": "Am<PERSON><PERSON><PERSON>", "kpi": "KPIs", "charts": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "data": "Ifintu"}, "settings": {"general": "Fyonse", "users": "Abomfwi", "advanced": "<PERSON><PERSON><PERSON><PERSON>"}, "common": {"create": "Panga", "edit": "<PERSON><PERSON><PERSON>", "delete": "<PERSON><PERSON>", "view": "Mona", "save": "<PERSON><PERSON>", "cancel": "Le<PERSON>", "back": "B<PERSON><PERSON>", "next": "<PERSON><PERSON><PERSON>", "previous": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "search": "Fwaya", "filter": "Salanganya", "sort": "<PERSON><PERSON>", "actions": "Incito", "status": "<PERSON><PERSON><PERSON><PERSON>", "date": "<PERSON><PERSON><PERSON><PERSON>", "time": "<PERSON><PERSON><PERSON>", "description": "Uku<PERSON><PERSON><PERSON><PERSON>", "details": "Ifintu Fyonse", "name": "<PERSON><PERSON><PERSON>", "email": "Email", "phone": "<PERSON><PERSON>", "address": "Keyala", "city": "<PERSON><PERSON><PERSON>", "country": "Icalo", "zip": "<PERSON><PERSON><PERSON><PERSON> ya Poso", "submit": "<PERSON><PERSON>", "reset": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "loading": "Ileleta...", "no_results": "Tapali <PERSON>", "success": "Nacicitika", "error": "Icilubo", "warning": "<PERSON><PERSON><PERSON><PERSON>", "info": "<PERSON><PERSON><PERSON>", "language": "<PERSON><PERSON><PERSON><PERSON>", "reports": "Am<PERSON><PERSON><PERSON>"}}