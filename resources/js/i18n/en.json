{"app": {"name": "TekRem ERP", "tagline": "Technology Remedies Innovations"}, "navigation": {"dashboard": "Dashboard", "profile": "Profile", "settings": "Settings", "logout": "Logout", "login": "<PERSON><PERSON>", "register": "Register", "home": "Home", "about": "About", "services": "Services", "portfolio": "Portfolio", "contact": "Contact", "notifications": "Notifications"}, "auth": {"email": "Email", "password": "Password", "remember_me": "Remember me", "forgot_password": "Forgot your password?", "login": "<PERSON><PERSON>", "register": "Register", "name": "Name", "confirm_password": "Confirm Password", "already_registered": "Already registered?", "reset_password": "Reset Password", "verify_email": "<PERSON><PERSON><PERSON>", "resend_verification_email": "Resend Verification Email", "verification_link_sent": "A new verification link has been sent to your email address."}, "dashboard": {"title": "Dashboard", "welcome": "Welcome to your dashboard", "stats": "Stats", "recent_activity": "Recent Activity", "quick_actions": "Quick Actions"}, "admin": {"users": "Users", "roles": "Roles", "permissions": "Permissions", "settings": "Settings", "logs": "Logs", "system": "System"}, "crm": {"title": "CRM", "clients": "Clients", "leads": "Leads", "communications": "Communications", "opportunities": "Opportunities", "chats": "Chats", "chat": "LiveChat", "conversation": "Conversation", "analytics": "Analytics", "dashboard": "Dashboard"}, "finance": {"title": "Finance", "accounts": "Accounts", "transactions": "Transactions", "invoices": "Invoices", "payments": "Payments", "quotations": "Quotations", "expenses": "Expenses", "budgets": "Budgets", "categories": "Categories", "reports": "Reports", "income_statement": "Income Statement", "cash_flow": "Cash Flow", "balance_sheet": "Balance Sheet", "expense_report": "Expense Report", "total_balance": "Total Balance", "monthly_income": "Monthly Income", "monthly_expenses": "Monthly Expenses", "net_income": "Net Income", "pending_invoices": "Pending Invoices", "overdue_invoices": "Overdue Invoices"}, "hr": {"title": "HR", "employees": "Employees", "leave": "Leave", "attendance": "Attendance", "performance": "Performance"}, "projects": {"title": "Projects", "projects": "Projects", "tasks": "Tasks", "timelines": "Timelines", "resources": "Resources"}, "support": {"title": "Support", "tickets": "Tickets", "knowledge_base": "Knowledge Base", "faq": "FAQ"}, "analytics": {"title": "Analytics", "reports": "Reports", "kpi": "KPIs", "charts": "Charts", "data": "Data"}, "ai": {"title": "AI", "dashboard": "Dashboard", "services": "Services", "models": "Models", "conversations": "Conversations", "prompt_templates": "Prompt Templates", "analytics": "Analytics", "usage_logs": "Usage Logs", "service_configuration": "Service Configuration", "model_management": "Model Management", "conversation_history": "Conversation History", "template_library": "Template Library", "usage_analytics": "Usage Analytics", "create_service": "Create Service", "edit_service": "Edit Service", "create_model": "Create Model", "edit_model": "Edit Model", "create_template": "Create Template", "edit_template": "Edit Template", "service_name": "Service Name", "service_type": "Service Type", "api_key": "API Key", "api_url": "API URL", "model_name": "Model Name", "model_type": "Model Type", "max_tokens": "<PERSON>", "temperature": "Temperature", "template_name": "Template Name", "template_content": "Template Content", "template_category": "Template Category", "prompt": "Prompt", "response": "Response", "tokens_used": "Tokens Used", "cost": "Cost", "provider": "Provider", "enabled": "Enabled", "disabled": "Disabled", "active": "Active", "inactive": "Inactive", "mistral": "Mistral AI", "openai": "OpenAI", "anthropic": "Anthropic", "test_connection": "Test Connection", "connection_successful": "Connection Successful", "connection_failed": "Connection Failed", "total_conversations": "Total Conversations", "total_tokens": "Total Tokens", "total_cost": "Total Cost", "avg_response_time": "Average Response Time", "success_rate": "Success Rate", "most_used_model": "Most Used Model", "conversation_created": "Conversation created successfully", "conversation_updated": "Conversation updated successfully", "conversation_deleted": "Conversation deleted successfully", "service_created": "Service created successfully", "service_updated": "Service updated successfully", "service_deleted": "Service deleted successfully", "model_created": "Model created successfully", "model_updated": "Model updated successfully", "model_deleted": "Model deleted successfully", "template_created": "Template created successfully", "template_updated": "Template updated successfully", "template_deleted": "Template deleted successfully"}, "cms": {"cms_title": "CMS", "cms_dashboard": "CMS Dashboard", "dashboard_description": "Manage your website content and monitor performance", "pages": "Pages", "pages_description": "Manage your website pages and content", "media": "Media Library", "media_description": "Manage your media files and assets", "templates": "Templates", "templates_description": "Manage page templates and layouts", "menus": "Menus", "menus_description": "Manage site navigation and menus", "redirects": "Redirects", "redirects_description": "Manage URL redirects and aliases", "cms_analytics": "Analytics", "analytics_description": "View content performance and statistics", "sitemap": "Sitemap", "sitemap_description": "Generate and manage XML sitemaps", "create_page": "Create Page", "edit_page": "Edit Page", "page_content": "Page Content", "page_settings": "Page Settings", "page_title": "Title", "slug": "Slug", "page_content_field": "Content", "excerpt": "Excerpt", "template": "Template", "layout": "Layout", "page_status": "Status", "published": "Published", "draft": "Draft", "scheduled": "Scheduled", "archived": "Archived", "publish": "Publish", "unpublish": "Unpublish", "save_draft": "Save Draft", "preview": "Preview", "duplicate": "Duplicate", "page_language": "Language", "parent_page": "<PERSON><PERSON>", "set_as_homepage": "Set as homepage", "show_in_menu": "Show in menu", "require_authentication": "Require authentication", "publishing": "Publishing", "schedule_for": "Schedule for", "schedule": "Schedule", "cms_save": "Save", "seo": "SEO", "seo_optimization": "SEO Optimization", "seo_description": "Optimize your page for search engines", "meta_title": "Meta Title", "meta_description": "Meta Description", "meta_keywords": "Meta Keywords", "og_title": "Open Graph Title", "og_description": "Open Graph Description", "og_image": "Open Graph Image", "canonical_url": "Canonical URL", "seo_analysis": "SEO Analysis", "seo_score": "SEO Score", "excellent": "Excellent", "good": "Good", "needs_improvement": "Needs Improvement", "poor": "Poor", "upload_media": "Upload Media", "media_library": "Media Library", "select_media": "Select Media", "browse": "Browse", "upload": "Upload", "drop_files_here": "Drop files here", "or_click_to_browse": "or click to browse", "choose_files": "<PERSON><PERSON>", "uploading": "Uploading...", "selected": "selected", "select_files": "Select Files", "no_media_found": "No media files found", "all_folders": "All folders", "template_layout": "Template & Layout", "all_templates": "All templates", "create_template": "Create Template", "edit_template": "Edit Template", "cms_quick_actions": "Quick Actions", "quick_actions_description": "Common tasks and shortcuts", "content_overview": "Content Overview", "content_overview_description": "Content distribution and performance", "content_by_status": "Content by Status", "seo_scores": "SEO Scores", "cms_recent_activity": "Recent Activity", "recent_activity_description": "Latest content changes and updates", "top_pages": "Top Pages", "top_pages_description": "Most viewed pages this month", "no_recent_activity": "No recent activity", "no_page_views": "No page views yet", "all_statuses": "All statuses", "all_languages": "All languages", "all_authors": "All authors", "search_pages": "Search pages...", "pages_selected": "pages selected", "pages_list": "Pages List", "total_pages": "total pages", "no_pages_found": "No pages found", "create_first_page": "Create your first page to get started", "page_created_successfully": "Page created successfully", "page_updated_successfully": "Page updated successfully", "page_deleted_successfully": "Page deleted successfully", "page_creation_failed": "Failed to create page", "media_uploaded_successfully": "Media uploaded successfully", "template_created_successfully": "Template created successfully", "menu_created_successfully": "<PERSON><PERSON> created successfully", "redirect_created_successfully": "Redirect created successfully", "enter_title": "Enter page title", "enter_slug": "Enter page slug", "enter_excerpt": "Enter page excerpt", "enter_meta_title": "Enter meta title", "enter_meta_description": "Enter meta description", "enter_og_title": "Enter Open Graph title", "enter_og_description": "Enter Open Graph description", "enter_og_image_url": "Enter image URL", "enter_canonical_url": "Enter canonical URL", "add_keyword": "Add keyword and press Enter", "select_parent": "Select parent page", "no_parent": "No parent", "design": "Design", "cms_settings": "Settings", "active": "active", "total_size": "total size", "total_hits": "total hits", "archive": "Archive"}, "settings": {"general": "General", "users": "Users", "advanced": "Advanced"}, "common": {"create": "Create", "edit": "Edit", "delete": "Delete", "view": "View", "save": "Save", "cancel": "Cancel", "back": "Back", "next": "Next", "previous": "Previous", "search": "Search", "filter": "Filter", "sort": "Sort", "actions": "Actions", "status": "Status", "date": "Date", "time": "Time", "description": "Description", "details": "Details", "name": "Name", "email": "Email", "phone": "Phone", "address": "Address", "city": "City", "country": "Country", "zip": "ZIP/Postal Code", "submit": "Submit", "reset": "Reset", "loading": "Loading...", "no_results": "No results found", "success": "Success", "error": "Error", "warning": "Warning", "info": "Information", "language": "Language", "reports": "Reports"}}